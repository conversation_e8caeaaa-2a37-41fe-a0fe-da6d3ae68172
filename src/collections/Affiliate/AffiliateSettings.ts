// payload.config.ts (or in a separate file imported into payload.config.ts)
import type { CollectionConfig } from 'payload';

export const AffiliateSettings: CollectionConfig = {
  slug: 'affiliate-settings',
  labels: {
    singular: 'Affiliate Setting',
    plural: 'Affiliate Settings',
  },
  admin: {
    useAsTitle: 'name',
  },
  access: {
  },
  fields: [
    {
      name: 'name',
      label: 'Setting Name',
      type: 'text',
      required: true,
      admin: {
        description: 'A descriptive name for this affiliate configuration (e.g. "Standard Affiliate Program").',
      },
    },
    {
      name: 'affiliate',
      type: 'relationship',
      relationTo: 'users', // or 'affiliates' if you have a separate collection
      required: true,
      admin: {
        description: 'Which affiliate this setting applies to.',
      },
      filterOptions: ({ user }) => {
        return {
          role: {
            equals: 'affiliate',
          },
        }
      },
    },
    {
      name: 'commissionPercent',
      label: 'Commission (% of net revenue)',
      type: 'number',
      min: 0,
      max: 100,
      required: false,
      admin: {
        description: 'Percentage of net revenue to pay the affiliate per sale. Leave empty or 0 if not using percentage-based commission.',
      },
    },
    {
      name: 'rewardTiers',
      label: 'Bonus Ticket Reward Tiers',
      type: 'array',
      required: false,
      fields: [
        {
          name: 'ticketsSoldThreshold',
          label: 'Tickets Sold Threshold',
          type: 'number',
          required: true,
          min: 1,
          admin: {
            description: 'Number of sold tickets after which this reward is granted (e.g., 20).',
          },
        },
        {
          name: 'eligibleTicketTiers',
          label: 'Eligible Ticket Tiers',
          type: 'relationship',
          relationTo: 'ticket-tiers',
          hasMany: true,
          required: true,
          admin: {
            description: 'Which ticket tiers count toward this threshold.',
          },
        },
        {
          name: 'rewardType',
          label: 'Reward Type',
          type: 'select',
          required: true,
          options: [
            { label: 'Percentage of order value', value: 'percentage' },
            { label: 'Free Ticket', value: 'free_ticket' },
          ],
          admin: {
            description: 'Choose whether the reward is a percentage bonus or a free ticket.',
          },
        },
        {
          name: 'rewardPercent',
          label: 'Reward Percent (%)',
          type: 'number',
          required: false,
          min: 0,
          max: 100,
          admin: {
            condition: (_, siblingData) => siblingData.rewardType === 'percentage',
            description: 'If reward type is percentage, this is the percentage of order value to reward.',
          },
        },
        {
          name: 'freeTicketCount',
          label: 'Free Ticket Count',
          type: 'number',
          required: false,
          min: 1,
          admin: {
            condition: (_, siblingData) => siblingData.rewardType === 'free_ticket',
            description: 'If reward type is free ticket, how many free tickets to grant when threshold is reached.',
          },
        },
        {
          name: 'freeTicketTier',
          label: 'Free Ticket Tier',
          type: 'relationship',
          relationTo: 'ticket-tiers',
          required: false,
          admin: {
            condition: (_, siblingData) => siblingData.rewardType === 'free_ticket',
            description: 'Which ticket tier to issue as the free ticket.',
          },
        },
      ],
      admin: {
        description: 'Define one or more tiers: after selling X tickets in eligible tiers, grant the defined reward.',
      },
    },
    {
      name: 'ticketTiersOverride',
      label: 'Global Eligible Ticket Tiers (Optional)',
      type: 'relationship',
      relationTo: 'ticket-tiers',
      hasMany: true,
      required: false,
      admin: {
        description: 'If you want a global list of ticket tiers that count for all reward tiers by default. Individual reward tiers may override via their own “Eligible Ticket Tiers.”',
      },
    },
    {
      name: 'notes',
      label: 'Notes / Description',
      type: 'textarea',
      required: false,
      admin: {
        description: 'Any additional notes about this affiliate setting.',
      },
    },
  ],
};
